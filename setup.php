<?php
/**
 * Setup Script for Weight Loss Dashboard
 */

// Check if setup is already completed
if (file_exists('backend/.env') && file_exists('backend/setup_complete.flag')) {
    die('Setup already completed. Delete backend/setup_complete.flag to run setup again.');
}

$step = $_GET['step'] ?? 1;
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($step) {
        case 1:
            // Database configuration
            $dbHost = $_POST['db_host'] ?? 'localhost';
            $dbName = $_POST['db_name'] ?? 'weight_loss_dashboard';
            $dbUser = $_POST['db_user'] ?? 'root';
            $dbPass = $_POST['db_pass'] ?? '';
            
            // Test database connection
            try {
                $pdo = new PDO("mysql:host=$dbHost", $dbUser, $dbPass);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // Create database if it doesn't exist
                $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbName`");
                $pdo->exec("USE `$dbName`");
                
                // Create .env file
                $envContent = "APP_ENV=production\n";
                $envContent .= "APP_URL=http://localhost\n";
                $envContent .= "TIMEZONE=UTC\n\n";
                $envContent .= "DB_HOST=$dbHost\n";
                $envContent .= "DB_NAME=$dbName\n";
                $envContent .= "DB_USER=$dbUser\n";
                $envContent .= "DB_PASS=$dbPass\n\n";
                $envContent .= "JWT_SECRET=" . bin2hex(random_bytes(32)) . "\n\n";
                $envContent .= "VIMEO_ACCESS_TOKEN=\n";
                $envContent .= "VIMEO_CLIENT_ID=\n";
                $envContent .= "VIMEO_CLIENT_SECRET=\n";
                
                file_put_contents('backend/.env', $envContent);
                
                $success = 'Database connection successful!';
                $step = 2;
                
            } catch (PDOException $e) {
                $error = 'Database connection failed: ' . $e->getMessage();
            }
            break;
            
        case 2:
            // Run database migrations
            try {
                require_once 'backend/config/config.php';
                require_once 'backend/config/database.php';
                
                $database = new Database();
                $db = $database->getConnection();
                
                // Read and execute schema
                $schema = file_get_contents('backend/database/schema.sql');
                $statements = explode(';', $schema);
                
                foreach ($statements as $statement) {
                    $statement = trim($statement);
                    if (!empty($statement)) {
                        $db->exec($statement);
                    }
                }
                
                $success = 'Database tables created successfully!';
                $step = 3;
                
            } catch (Exception $e) {
                $error = 'Database setup failed: ' . $e->getMessage();
            }
            break;
            
        case 3:
            // Admin user setup
            $username = $_POST['admin_username'] ?? '';
            $email = $_POST['admin_email'] ?? '';
            $password = $_POST['admin_password'] ?? '';
            
            if (empty($username) || empty($email) || empty($password)) {
                $error = 'All fields are required';
            } else {
                try {
                    require_once 'backend/config/config.php';
                    require_once 'backend/config/database.php';
                    
                    $database = new Database();
                    $db = $database->getConnection();
                    
                    // Update admin user
                    $passwordHash = hashPassword($password);
                    $stmt = $db->prepare("
                        UPDATE admins 
                        SET username = ?, email = ?, password_hash = ? 
                        WHERE id = 1
                    ");
                    $stmt->execute([$username, $email, $passwordHash]);
                    
                    // Create setup complete flag
                    file_put_contents('backend/setup_complete.flag', date('Y-m-d H:i:s'));
                    
                    $success = 'Setup completed successfully!';
                    $step = 4;
                    
                } catch (Exception $e) {
                    $error = 'Admin setup failed: ' . $e->getMessage();
                }
            }
            break;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weight Loss Dashboard - Setup</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .setup-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card setup-card border-0">
                    <div class="card-header bg-primary text-white text-center py-4">
                        <h3 class="mb-0">Weight Loss Dashboard Setup</h3>
                        <p class="mb-0 mt-2 opacity-75">Step <?php echo $step; ?> of 4</p>
                    </div>
                    <div class="card-body p-5">
                        <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo htmlspecialchars($error); ?>
                        </div>
                        <?php endif; ?>

                        <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo htmlspecialchars($success); ?>
                        </div>
                        <?php endif; ?>

                        <?php if ($step == 1): ?>
                        <h4>Database Configuration</h4>
                        <p class="text-muted">Configure your database connection settings.</p>
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label for="db_host" class="form-label">Database Host</label>
                                <input type="text" class="form-control" id="db_host" name="db_host" value="localhost" required>
                            </div>
                            <div class="mb-3">
                                <label for="db_name" class="form-label">Database Name</label>
                                <input type="text" class="form-control" id="db_name" name="db_name" value="weight_loss_dashboard" required>
                            </div>
                            <div class="mb-3">
                                <label for="db_user" class="form-label">Database Username</label>
                                <input type="text" class="form-control" id="db_user" name="db_user" value="root" required>
                            </div>
                            <div class="mb-3">
                                <label for="db_pass" class="form-label">Database Password</label>
                                <input type="password" class="form-control" id="db_pass" name="db_pass">
                            </div>
                            <button type="submit" class="btn btn-primary">Test Connection & Continue</button>
                        </form>

                        <?php elseif ($step == 2): ?>
                        <h4>Database Setup</h4>
                        <p class="text-muted">Create database tables and initial data.</p>
                        
                        <form method="POST">
                            <p>Click the button below to create the necessary database tables.</p>
                            <button type="submit" class="btn btn-primary">Create Database Tables</button>
                        </form>

                        <?php elseif ($step == 3): ?>
                        <h4>Admin Account</h4>
                        <p class="text-muted">Create your administrator account.</p>
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label for="admin_username" class="form-label">Username</label>
                                <input type="text" class="form-control" id="admin_username" name="admin_username" required>
                            </div>
                            <div class="mb-3">
                                <label for="admin_email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="admin_email" name="admin_email" required>
                            </div>
                            <div class="mb-3">
                                <label for="admin_password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="admin_password" name="admin_password" required>
                            </div>
                            <button type="submit" class="btn btn-primary">Create Admin Account</button>
                        </form>

                        <?php elseif ($step == 4): ?>
                        <h4>Setup Complete!</h4>
                        <p class="text-success">Your Weight Loss Dashboard has been set up successfully.</p>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h5>Admin Panel</h5>
                                        <p class="text-muted">Manage users and courses</p>
                                        <a href="backend/admin/" class="btn btn-primary">Access Admin Panel</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h5>User App</h5>
                                        <p class="text-muted">Flutter PWA frontend</p>
                                        <a href="frontend/web/" class="btn btn-success">View User App</a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4">
                            <h6>Next Steps:</h6>
                            <ul class="text-muted">
                                <li>Configure your Vimeo API credentials in the .env file</li>
                                <li>Build the Flutter web app: <code>flutter build web</code></li>
                                <li>Create your first course and add users</li>
                            </ul>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
