<?php
session_start();
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/Auth.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$auth = new Auth();
$database = new Database();
$db = $database->getConnection();

$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'add_user':
            $email = sanitizeInput($_POST['email']);
            $phone = sanitizeInput($_POST['phone']);
            $fullName = sanitizeInput($_POST['full_name']);
            $age = (int)$_POST['age'];
            $weight = (float)$_POST['weight'];
            $height = (float)$_POST['height'];
            $targetWeight = (float)$_POST['target_weight'];
            $courseId = (int)$_POST['course_id'];
            
            if (validateEmail($email)) {
                try {
                    $db->beginTransaction();
                    
                    // Generate unique token
                    $uniqueToken = generateUniqueToken(32);
                    
                    // Insert user
                    $stmt = $db->prepare("
                        INSERT INTO users (email, phone, full_name, age, weight, height, target_weight, unique_token, created_by) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([$email, $phone, $fullName, $age, $weight, $height, $targetWeight, $uniqueToken, $_SESSION['admin_id']]);
                    $userId = $db->lastInsertId();
                    
                    // Assign course if selected
                    if ($courseId > 0) {
                        $stmt = $db->prepare("INSERT INTO user_courses (user_id, course_id) VALUES (?, ?)");
                        $stmt->execute([$userId, $courseId]);
                    }
                    
                    $db->commit();
                    $message = 'User added successfully!';
                    $messageType = 'success';
                    
                } catch (Exception $e) {
                    $db->rollback();
                    $message = 'Error adding user: ' . $e->getMessage();
                    $messageType = 'danger';
                }
            } else {
                $message = 'Invalid email address';
                $messageType = 'danger';
            }
            break;
            
        case 'generate_link':
            $userId = (int)$_POST['user_id'];
            $link = $auth->generateAuthLink($userId, $_SESSION['admin_id']);
            if ($link) {
                $message = 'Authentication link generated: <br><strong>' . $link . '</strong>';
                $messageType = 'success';
            } else {
                $message = 'Error generating authentication link';
                $messageType = 'danger';
            }
            break;
            
        case 'revoke_access':
            $userId = (int)$_POST['user_id'];
            if ($auth->revokeUserAccess($userId)) {
                $message = 'User access revoked successfully';
                $messageType = 'success';
            } else {
                $message = 'Error revoking user access';
                $messageType = 'danger';
            }
            break;
    }
}

// Get all users with their course assignments and progress
$stmt = $db->query("
    SELECT u.*, 
           c.title as course_title,
           uc.assigned_at,
           uc.started_at,
           uc.completed_at,
           COUNT(uvp.id) as videos_watched,
           AVG(uvp.completion_percentage) as avg_completion
    FROM users u
    LEFT JOIN user_courses uc ON u.id = uc.user_id AND uc.is_active = 1
    LEFT JOIN courses c ON uc.course_id = c.id
    LEFT JOIN user_video_progress uvp ON u.id = uvp.user_id
    GROUP BY u.id
    ORDER BY u.created_at DESC
");
$users = $stmt->fetchAll();

// Get all courses for the dropdown
$stmt = $db->query("SELECT id, title FROM courses WHERE is_active = 1 ORDER BY title");
$courses = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 15px 20px;
            border-radius: 8px;
            margin: 5px 10px;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: white;
        }
        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        .progress-bar-custom {
            height: 8px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">Admin Panel</h4>
                        <small class="text-white-50">Weight Loss Dashboard</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="index.php">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="users.php">
                                <i class="fas fa-users me-2"></i>
                                Users
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="courses.php">
                                <i class="fas fa-play-circle me-2"></i>
                                Courses
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="analytics.php">
                                <i class="fas fa-chart-bar me-2"></i>
                                Analytics
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="settings.php">
                                <i class="fas fa-cog me-2"></i>
                                Settings
                            </a>
                        </li>
                        <li class="nav-item mt-4">
                            <a class="nav-link" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">User Management</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                            <i class="fas fa-user-plus me-2"></i>
                            Add New User
                        </button>
                    </div>
                </div>

                <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- Users Table -->
                <div class="card shadow">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">All Users</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered" id="usersTable" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Course</th>
                                        <th>Progress</th>
                                        <th>Status</th>
                                        <th>Last Login</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar me-3">
                                                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                        <?php echo strtoupper(substr($user['full_name'], 0, 2)); ?>
                                                    </div>
                                                </div>
                                                <div>
                                                    <div class="fw-bold"><?php echo htmlspecialchars($user['full_name']); ?></div>
                                                    <small class="text-muted">Age: <?php echo $user['age']; ?> | <?php echo $user['weight']; ?>kg</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td><?php echo htmlspecialchars($user['email']); ?></td>
                                        <td>
                                            <?php if ($user['course_title']): ?>
                                                <span class="badge bg-info"><?php echo htmlspecialchars($user['course_title']); ?></span>
                                            <?php else: ?>
                                                <span class="text-muted">No course assigned</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($user['videos_watched'] > 0): ?>
                                                <div class="progress progress-bar-custom">
                                                    <div class="progress-bar bg-success" style="width: <?php echo min(100, $user['avg_completion']); ?>%"></div>
                                                </div>
                                                <small class="text-muted"><?php echo $user['videos_watched']; ?> videos watched</small>
                                            <?php else: ?>
                                                <span class="text-muted">Not started</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($user['is_active']): ?>
                                                <span class="badge bg-success status-badge">Active</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger status-badge">Inactive</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($user['last_login']): ?>
                                                <?php echo date('M j, Y', strtotime($user['last_login'])); ?>
                                            <?php else: ?>
                                                <span class="text-muted">Never</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="generateLink(<?php echo $user['id']; ?>)">
                                                    <i class="fas fa-link"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-info" onclick="viewUser(<?php echo $user['id']; ?>)">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="revokeAccess(<?php echo $user['id']; ?>)">
                                                    <i class="fas fa-ban"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Add User Modal -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add_user">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="full_name" class="form-label">Full Name *</label>
                                    <input type="text" class="form-control" id="full_name" name="full_name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email *</label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">Phone</label>
                                    <input type="tel" class="form-control" id="phone" name="phone">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="age" class="form-label">Age *</label>
                                    <input type="number" class="form-control" id="age" name="age" min="1" max="120" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="weight" class="form-label">Current Weight (kg) *</label>
                                    <input type="number" class="form-control" id="weight" name="weight" step="0.1" min="1" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="height" class="form-label">Height (cm) *</label>
                                    <input type="number" class="form-control" id="height" name="height" step="0.1" min="1" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="target_weight" class="form-label">Target Weight (kg)</label>
                                    <input type="number" class="form-control" id="target_weight" name="target_weight" step="0.1" min="1">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="course_id" class="form-label">Assign Course</label>
                            <select class="form-select" id="course_id" name="course_id">
                                <option value="0">No course assigned</option>
                                <?php foreach ($courses as $course): ?>
                                <option value="<?php echo $course['id']; ?>"><?php echo htmlspecialchars($course['title']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add User</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Hidden forms for actions -->
    <form id="generateLinkForm" method="POST" action="" style="display: none;">
        <input type="hidden" name="action" value="generate_link">
        <input type="hidden" name="user_id" id="generateLinkUserId">
    </form>

    <form id="revokeAccessForm" method="POST" action="" style="display: none;">
        <input type="hidden" name="action" value="revoke_access">
        <input type="hidden" name="user_id" id="revokeAccessUserId">
    </form>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

    <script>
        $(document).ready(function() {
            $('#usersTable').DataTable({
                "pageLength": 25,
                "order": [[ 5, "desc" ]],
                "columnDefs": [
                    { "orderable": false, "targets": 6 }
                ]
            });
        });

        function generateLink(userId) {
            if (confirm('Generate a new authentication link for this user?')) {
                document.getElementById('generateLinkUserId').value = userId;
                document.getElementById('generateLinkForm').submit();
            }
        }

        function revokeAccess(userId) {
            if (confirm('Are you sure you want to revoke access for this user? This will log them out from all devices.')) {
                document.getElementById('revokeAccessUserId').value = userId;
                document.getElementById('revokeAccessForm').submit();
            }
        }

        function viewUser(userId) {
            // Redirect to user detail page
            window.location.href = 'user-detail.php?id=' + userId;
        }
    </script>
</body>
</html>
