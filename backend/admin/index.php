<?php
session_start();
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/Auth.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$auth = new Auth();
$database = new Database();
$db = $database->getConnection();

// Get dashboard statistics
$stats = [];

// Total users
$stmt = $db->query("SELECT COUNT(*) as total FROM users WHERE is_active = 1");
$stats['total_users'] = $stmt->fetch()['total'];

// Total courses
$stmt = $db->query("SELECT COUNT(*) as total FROM courses WHERE is_active = 1");
$stats['total_courses'] = $stmt->fetch()['total'];

// Active sessions today
$stmt = $db->query("SELECT COUNT(*) as total FROM user_sessions WHERE DATE(started_at) = CURDATE()");
$stats['active_sessions_today'] = $stmt->fetch()['total'];

// Total watch time today (in minutes)
$stmt = $db->query("
    SELECT COALESCE(SUM(duration_seconds), 0) / 60 as total_minutes 
    FROM user_activity_log 
    WHERE activity_type = 'video_complete' AND DATE(created_at) = CURDATE()
");
$stats['watch_time_today'] = round($stmt->fetch()['total_minutes'], 2);

// Recent user activity
$stmt = $db->query("
    SELECT u.full_name, u.email, ual.activity_type, ual.created_at, cv.title as video_title
    FROM user_activity_log ual
    JOIN users u ON ual.user_id = u.id
    LEFT JOIN course_videos cv ON ual.video_id = cv.id
    ORDER BY ual.created_at DESC
    LIMIT 10
");
$recent_activity = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Weight Loss Course</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 15px 20px;
            border-radius: 8px;
            margin: 5px 10px;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: white;
        }
        .stat-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border: none;
            border-radius: 15px;
            color: white;
        }
        .stat-card-2 {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .stat-card-3 {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        .stat-card-4 {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        .activity-item {
            border-left: 4px solid #667eea;
            padding-left: 15px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">Admin Panel</h4>
                        <small class="text-white-50">Weight Loss Dashboard</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="index.php">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="users.php">
                                <i class="fas fa-users me-2"></i>
                                Users
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="courses.php">
                                <i class="fas fa-play-circle me-2"></i>
                                Courses
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="analytics.php">
                                <i class="fas fa-chart-bar me-2"></i>
                                Analytics
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="settings.php">
                                <i class="fas fa-cog me-2"></i>
                                Settings
                            </a>
                        </li>
                        <li class="nav-item mt-4">
                            <a class="nav-link" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Dashboard</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-download me-1"></i>
                                Export
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Users</div>
                                        <div class="h5 mb-0 font-weight-bold"><?php echo $stats['total_users']; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card stat-card-2 h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Courses</div>
                                        <div class="h5 mb-0 font-weight-bold"><?php echo $stats['total_courses']; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-play-circle fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card stat-card-3 h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">Active Sessions Today</div>
                                        <div class="h5 mb-0 font-weight-bold"><?php echo $stats['active_sessions_today']; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-clock fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card stat-card-4 h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">Watch Time Today</div>
                                        <div class="h5 mb-0 font-weight-bold"><?php echo $stats['watch_time_today']; ?> min</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-video fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">Recent User Activity</h6>
                            </div>
                            <div class="card-body">
                                <?php foreach ($recent_activity as $activity): ?>
                                <div class="activity-item">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <strong><?php echo htmlspecialchars($activity['full_name']); ?></strong>
                                            <span class="text-muted"><?php echo htmlspecialchars($activity['email']); ?></span>
                                        </div>
                                        <small class="text-muted"><?php echo date('M j, Y g:i A', strtotime($activity['created_at'])); ?></small>
                                    </div>
                                    <div class="text-muted">
                                        <?php 
                                        switch($activity['activity_type']) {
                                            case 'login':
                                                echo '<i class="fas fa-sign-in-alt text-success"></i> Logged in';
                                                break;
                                            case 'video_start':
                                                echo '<i class="fas fa-play text-primary"></i> Started watching: ' . htmlspecialchars($activity['video_title']);
                                                break;
                                            case 'video_complete':
                                                echo '<i class="fas fa-check-circle text-success"></i> Completed: ' . htmlspecialchars($activity['video_title']);
                                                break;
                                            default:
                                                echo '<i class="fas fa-info-circle"></i> ' . ucfirst($activity['activity_type']);
                                        }
                                        ?>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                            </div>
                            <div class="card-body">
                                <a href="users.php?action=add" class="btn btn-primary btn-block mb-3">
                                    <i class="fas fa-user-plus me-2"></i>
                                    Add New User
                                </a>
                                <a href="courses.php?action=add" class="btn btn-success btn-block mb-3">
                                    <i class="fas fa-plus-circle me-2"></i>
                                    Create Course
                                </a>
                                <a href="analytics.php" class="btn btn-info btn-block mb-3">
                                    <i class="fas fa-chart-line me-2"></i>
                                    View Analytics
                                </a>
                                <a href="settings.php" class="btn btn-secondary btn-block">
                                    <i class="fas fa-cog me-2"></i>
                                    Settings
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
